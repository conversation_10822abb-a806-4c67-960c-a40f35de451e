<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import TopTitle from "@/views/components/TopTitle/index.vue";
import { Search, RefreshRight, Plus } from "@element-plus/icons-vue";
import { getSchoolList<PERSON>pi, addSchoolApi, updateSchoolApi, deleteSchoolApi, getSchoolDetailApi } from "@/apis/school";
import { getCityList, getAreaListByCity, getSchoolStageList } from '@/apis/common';

// 搜索表单数据
const searchForm = ref({
  stage: "", // 学校类型（学段）
  cityId: "", // 地市
  areaId: "", // 区县
  title: "", // 学校名称
});

// 学校类型选项（学段）
const schoolTypeOptions = ref([
  { label: "全部", value: "" }
]);

// 地市选项
const cityOptions = ref([
  { label: "全部", value: "" }
]);

// 区县选项
const districtOptions = ref([
  { label: "全部", value: "" }
]);

// 初始化数据加载
const initData = async () => {
  try {
    // 加载学段数据
    const stageRes = await getSchoolStageList();
    if (stageRes.data && Array.isArray(stageRes.data)) {
      schoolTypeOptions.value = stageRes.data
    }

    // 加载地市数据
    const cityRes = await getCityList();
    if (cityRes.data && Array.isArray(cityRes.data)) {
      cityOptions.value = stageRes.data
    }
  } catch (error) {
    console.error("初始化数据失败:", error);
  }
};

// 监听地市变化，加载对应区县数据
const handleCityChange = async (cityId) => {
  searchForm.value.areaId = ""; // 清空区县选择
  districtOptions.value = [{ label: "全部", value: "" }];

  if (cityId) {
    try {
      const areaRes = await getAreaListByCity(cityId);
      if (areaRes.data && Array.isArray(areaRes.data)) {
        districtOptions.value = areaRes.data
      }
    } catch (error) {
      console.error("加载区县数据失败:", error);
    }
  }
};

// 表单用的区县选项
const formDistrictOptions = ref([
  { label: "请选择区县", value: "" }
]);

// 表单中地市变化处理
const handleFormCityChange = async (cityId) => {
  schoolForm.value.areaId = ""; // 清空区县选择
  formDistrictOptions.value = [{ title: "请选择区县", id: "" }];

  if (cityId) {
    try {
      const areaRes = await getAreaListByCity(cityId);
      if (areaRes.data && Array.isArray(areaRes.data)) {
        formDistrictOptions.value = [{ title: "请选择区县", id: "" }];
      }
    } catch (error) {
      console.error("加载区县数据失败:", error);
    }
  }
};

// 搜索功能
const handleSearch = async () => {
  try {
    console.log("搜索参数:", searchForm.value);
    // 构建查询参数
    const params = {
      stage: searchForm.value.stage,
      cityId: searchForm.value.cityId,
      areaId: searchForm.value.areaId,
      title: searchForm.value.title,
      pageNum: pagination.value.currentPage,
      pageSize: pagination.value.pageSize,
    };

    // 调用API获取学校列表
    const { data } = await getSchoolListApi(params);
    tableData.value = data.list || [];
    pagination.value.total = data.total || 0;

  } catch (error) {
    ElMessage.error("搜索失败");
  }
};

// 重置功能
const handleReset = () => {
  searchForm.value = {
    stage: "",
    cityId: "",
    areaId: "",
    title: "",
  };
  console.log("表单已重置");
  // 重置后重新搜索
  handleSearch();
};

// 表格数据
const tableData = ref([]);

// 分页数据
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});

// 分页变化处理
const handleCurrentChange = (page) => {
  pagination.value.currentPage = page;
  console.log("当前页:", page);
};

// 刷新数据
const handleRefresh = () => {
  console.log("刷新数据");
  handleSearch();
};

// 弹窗相关状态
const dialogVisible = ref(false);
const dialogTitle = ref("");
const isEdit = ref(false);

// 学校表单数据
const schoolForm = ref({
  title: "", // 学校名称
  stage: "", // 学校类型（学段）
  cityId: "", // 所属地市
  areaId: "", // 所属区县
});

// 学校表单验证规则
const schoolFormRules = {
  title: [{ required: true, message: "请输入学校名称", trigger: "blur" }],
  stage: [
    { required: true, message: "请选择学校类型", trigger: "change" },
  ],
  cityId: [{ required: true, message: "请选择所属地市", trigger: "change" }],
  areaId: [{ required: true, message: "请选择所属区县", trigger: "change" }],
};

// 表单引用
const schoolFormRef = ref();

// 新增学校
const handleAddSchool = () => {
  dialogTitle.value = "新增学校";
  isEdit.value = false;
  schoolForm.value = {
    title: "",
    stage: "",
    cityId: "",
    areaId: "",
  };
  // 重置表单区县选项
  formDistrictOptions.value = [{ title: "请选择区县", id: "" }];
  dialogVisible.value = true;
};

// 导出数据
const handleExport = () => {
  console.log("导出数据");
  ElMessage.success("数据导出成功");
};

// 编辑学校
const handleEditSchool = async (row) => {
  try {
    dialogTitle.value = "编辑学校";
    isEdit.value = true;

    // 先调用查询详情接口
    const { data } = await getSchoolDetailApi(row.id);

    schoolForm.value = {
      id: data.id,
      title: data.title || data.schoolName,
      stage: data.stage || data.schoolType,
      cityId: data.cityId,
      areaId: data.areaId,
    };

    // 如果有cityId，加载对应的区县数据
    if (data.cityId) {
      await handleFormCityChange(data.cityId);
    }

    dialogVisible.value = true;
  } catch (error) {
    console.error("获取学校详情失败:", error);
    ElMessage.error("获取学校详情失败");
  }
};

// 关闭弹窗
const handleCloseDialog = () => {
  dialogVisible.value = false;
  schoolFormRef.value?.resetFields();
};

// 提交表单
const handleSubmitForm = async () => {
  try {
    await schoolFormRef.value.validate();

    const params = {
      title: schoolForm.value.title,
      stage: schoolForm.value.stage,
      cityId: schoolForm.value.cityId,
      areaId: schoolForm.value.areaId,
    };

    if (isEdit.value) {
      // 编辑学校逻辑 - 第一个参数传schoolId，第二个参数传对象
      await updateSchoolApi(schoolForm.value.id, params);
      ElMessage.success("学校信息修改成功");
    } else {
      // 新增学校逻辑
      await addSchoolApi(params);
      ElMessage.success("学校添加成功");
    }

    handleCloseDialog();
    // 刷新列表
    handleSearch();
  } catch (error) {
    console.error("操作失败:", error);
    ElMessage.error(isEdit.value ? "修改学校失败" : "新增学校失败");
  }
};

// 删除学校
const handleDeleteSchool = (row) => {
  ElMessageBox.confirm(`确定要删除学校"${row.schoolName || row.title}"吗？`, "删除确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
    customClass: "custom-confirm-box",
    confirmButtonClass: "confirm-btn",
    cancelButtonClass: "cancel-btn",
  })
    .then(async () => {
      try {
        // 调用删除API，传参schoolId
        await deleteSchoolApi(row.id);
        ElMessage.success("学校删除成功");
        // 刷新列表
        handleSearch();
      } catch (error) {
        console.error("删除学校失败:", error);
        ElMessage.error("删除学校失败");
      }
    })
    .catch(() => {
    });
};

// 操作处理
const handleOperation = async (row, action) => {
  if (action === "edit") {
    handleEditSchool(row);
  } else if (action === "delete") {
    handleDeleteSchool(row);
  }
};

// 组件挂载时初始化数据
onMounted(() => {
  initData();
  handleSearch();
});
</script>

<template>
  <div class="complaint-distribution app-container">
    <TopTitle title="学校管理"></TopTitle>
    <section class="search-form px-[24px] py-[16px] rounded-[2px] bg-[#fff]">
      <el-form :model="searchForm" class="search-form-4" label-position="top">
        <!-- 学校类型选择 -->
        <el-form-item label="学校类型">
          <el-select v-model="searchForm.stage" placeholder="全部">
            <el-option
              v-for="item in schoolTypeOptions"
              :key="item.id"
              :label="item.title"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <!-- 地市选择 -->
        <el-form-item label="地市">
          <el-select v-model="searchForm.cityId" placeholder="全部" @change="handleCityChange">
            <el-option
              v-for="item in cityOptions"
              :key="item.id"
              :label="item.title"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <!-- 区县选择 -->
        <el-form-item label="区县">
          <el-select v-model="searchForm.areaId" placeholder="全部">
            <el-option
              v-for="item in districtOptions"
              :key="item.id"
              :label="item.title"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <!-- 学校名称搜索 -->
        <el-form-item label="学校名称">
          <el-input
            v-model="searchForm.title"
            placeholder="请输入学校名称"
          />
        </el-form-item>

        <!-- 按钮组 - 第二行右对齐 -->
        <div class="button-row">
          <el-button class="common-button-3" @click="handleSearch">
            <template #icon>
              <el-icon>
                <Search />
              </el-icon>
            </template>
            查询
          </el-button>
          <el-button class="reset-button" @click="handleReset">
            <template #icon>
              <el-icon><RefreshRight /></el-icon>
            </template>
            重置
          </el-button>
        </div>
      </el-form>
    </section>
    <section
      class="table-container mt-[16px] px-[24px] py-[16px] rounded-[2px] bg-[#fff]"
    >
      <!-- 操作按钮 -->
      <div class="mb-[16px] flex justify-between">
        <el-button class="common-button-4" @click="handleRefresh">
          <template #icon>
            <el-icon><RefreshRight /></el-icon>
          </template>
          刷新
        </el-button>
        <div class="flex gap-[16px]">
          <el-button class="common-button-3" @click="handleAddSchool">
            <template #icon>
              <el-icon><Plus /></el-icon>
            </template>
            新增学校
          </el-button>
          <el-button class="common-button-3" @click="handleExport">
            <template #icon>
              <img
                src="@/assets/images/common/export.png"
                alt=""
                width="16"
                height="16"
              />
            </template>
            批量导入
          </el-button>
        </div>
      </div>

      <!-- 表格 -->
      <el-table
        :data="tableData"
        style="width: 100%"
        class="custom-table"
        stripe
      >
        <!-- 序号 -->
        <el-table-column prop="id" label="序号" width="80" align="center" />

        <!-- 学校名称 -->
        <el-table-column
          prop="schoolName"
          label="学校名称"
          min-width="200"
          align="center"
        />

        <!-- 学校类型 -->
        <el-table-column
          prop="schoolType"
          label="学校类型"
          min-width="120"
          align="center"
        />

        <!-- 地市 -->
        <el-table-column
          prop="city"
          label="地市"
          min-width="120"
          align="center"
        />

        <!-- 区县 -->
        <el-table-column
          prop="district"
          label="区县"
          min-width="120"
          align="center"
        />

        <!-- 创建时间 -->
        <el-table-column
          prop="createTime"
          label="创建时间"
          min-width="140"
          align="center"
        />

        <!-- 操作列 -->
        <el-table-column label="操作" width="200" align="center">
          <template #default="{ row }">
            <div class="flex-center-center gap-[16px]">
              <div class="pointer blue2" @click="handleOperation(row, 'edit')">
                编辑
              </div>
              <div class="pointer red1" @click="handleOperation(row, 'delete')">
                删除
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器 -->
      <div class="mt-[16px] flex justify-end">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          :total="pagination.total"
          layout="total, prev, pager, next"
          @current-change="handleCurrentChange"
        />
      </div>
    </section>

    <!-- 学校弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="480px"
      class="custom-dialog"
      :before-close="handleCloseDialog"
    >
      <el-form
        ref="schoolFormRef"
        :model="schoolForm"
        :rules="schoolFormRules"
        label-position="top"
        class="school-form"
      >
        <el-form-item label="学校名称" prop="title" required>
          <el-input
            v-model="schoolForm.title"
            placeholder="请输入学校名称"
            maxlength="50"
          />
        </el-form-item>

        <el-form-item label="学校类型" prop="stage" required>
          <el-select
            v-model="schoolForm.stage"
            placeholder="请选择学校类型"
            style="width: 100%"
          >
            <el-option
              v-for="item in schoolTypeOptions"
              :key="item.id"
              :label="item.title"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="所属地市" prop="cityId" required>
          <el-select
            v-model="schoolForm.cityId"
            placeholder="请选择所属地市"
            style="width: 100%"
            @change="handleFormCityChange"
          >
            <el-option
              v-for="item in cityOptions"
              :key="item.id"
              :label="item.title"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="所属区县" prop="areaId" required>
          <el-select
            v-model="schoolForm.areaId"
            placeholder="请选择所属区县"
            style="width: 100%"
          >
            <el-option
              v-for="item in formDistrictOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button class="cancel-btn" @click="handleCloseDialog"
            >取消</el-button
          >
          <el-button class="confirm-btn" @click="handleSubmitForm"
            >确认</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.hidden-label {
  :deep(.el-form-item__label) {
    display: none;
  }
}

.whitespace-pre-line {
  white-space: pre-line;
  font-size: 16px;
  line-height: 24px;
}

// 学校表单样式
.school-form {
  .el-form-item {
    margin-bottom: 20px;

    .el-form-item__label {
      color: var(--grey1);
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
      margin-bottom: 8px;
    }

    .el-input__wrapper {
      height: 48px;
      font-size: 16px;
    }

    .el-select {
      .el-select__wrapper {
        height: 48px;
        font-size: 16px;
      }
    }
  }
}
</style>
