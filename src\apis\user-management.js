import { request } from "@/utils/axios"

/** 分页查询用户列表 */
export function getUserListApi(params) {
  return request({
    url: "system/admin/list",
    method: "get",
    params,
  });
}

/** 新增用户 */
export function createUserApi(data) {
  return request({
    url: "system/admin",
    method: "post",
    data,
  });
}

/** 更新用户信息 */
export function updateUserApi(userId, data) {
  return request({
    url: `system/admin/${userId}`,
    method: "put",
    data,
  });
}

/** 重置用户密码 */
export function resetUserPasswordApi(userId, data) {
  return request({
    url: `system/user/${userId}/reset-password`,
    method: "patch",
    data,
  });
}

/** 获取用户详情 */
export function getUserDetailApi(userId) {
  return request({
    url: `system/admin/${userId}`,
    method: "get",
  });
}
